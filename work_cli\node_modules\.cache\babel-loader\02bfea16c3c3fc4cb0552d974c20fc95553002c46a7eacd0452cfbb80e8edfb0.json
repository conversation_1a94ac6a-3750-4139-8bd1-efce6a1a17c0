{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive, onMounted } from 'vue';\nimport { useRouter, useRoute } from 'vue-router';\nimport { teamAPI } from '@/api';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nexport default {\n  name: 'TeamEditView',\n  setup() {\n    const router = useRouter();\n    const route = useRoute();\n    const formRef = ref();\n    const loading = ref(false);\n    const submitting = ref(false);\n    const stoppingRecruiting = ref(false);\n    const team = ref(null);\n    const form = reactive({\n      name: '',\n      description: '',\n      maxMembers: 5\n    });\n    const rules = {\n      name: [{\n        required: true,\n        message: '请输入团队名称',\n        trigger: 'blur'\n      }, {\n        min: 2,\n        max: 50,\n        message: '团队名称长度在 2 到 50 个字符',\n        trigger: 'blur'\n      }],\n      description: [{\n        required: true,\n        message: '请输入团队描述',\n        trigger: 'blur'\n      }, {\n        min: 10,\n        max: 500,\n        message: '团队描述长度在 10 到 500 个字符',\n        trigger: 'blur'\n      }],\n      maxMembers: [{\n        required: true,\n        message: '请输入最大成员数',\n        trigger: 'blur'\n      }]\n    };\n    const fetchTeam = async () => {\n      try {\n        loading.value = true;\n        const teamId = route.params.id;\n        const response = await teamAPI.getTeam(teamId);\n        team.value = response;\n        Object.assign(form, {\n          name: team.value.name,\n          description: team.value.description,\n          maxMembers: team.value.maxMembers\n        });\n      } catch (error) {\n        console.error('Fetch team error:', error);\n        ElMessage.error('获取团队信息失败: ' + (error.message || '未知错误'));\n      } finally {\n        loading.value = false;\n      }\n    };\n    const handleSubmit = async () => {\n      if (!formRef.value) return;\n      try {\n        await formRef.value.validate();\n        submitting.value = true;\n        const teamId = route.params.id;\n        await teamAPI.updateTeam(teamId, form);\n        ElMessage.success('团队更新成功！');\n        router.push('/dashboard/my-teams');\n      } catch (error) {\n        console.error('Update team error:', error);\n        ElMessage.error('更新团队失败');\n      } finally {\n        submitting.value = false;\n      }\n    };\n    const handleStopRecruiting = async () => {\n      try {\n        await ElMessageBox.confirm('停止招募后，其他用户将无法申请加入团队，但团队可以申请项目。确定要停止招募吗？', '停止招募确认', {\n          confirmButtonText: '确定停止',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        stoppingRecruiting.value = true;\n        const teamId = route.params.id;\n        await teamAPI.stopRecruiting(teamId);\n        ElMessage.success('停止招募成功！');\n        // 更新团队状态\n        team.value.status = 'RECRUITING_STOPPED';\n      } catch (error) {\n        if (error === 'cancel') {\n          return;\n        }\n        console.error('Stop recruiting error:', error);\n        ElMessage.error('停止招募失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n      } finally {\n        stoppingRecruiting.value = false;\n      }\n    };\n    const handleCancel = () => {\n      router.back();\n    };\n    onMounted(() => {\n      fetchTeam();\n    });\n    return {\n      formRef,\n      form,\n      rules,\n      loading,\n      submitting,\n      stoppingRecruiting,\n      team,\n      handleSubmit,\n      handleStopRecruiting,\n      handleCancel\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "useRouter", "useRoute", "teamAPI", "ElMessage", "ElMessageBox", "name", "setup", "router", "route", "formRef", "loading", "submitting", "stoppingRecruiting", "team", "form", "description", "maxMembers", "rules", "required", "message", "trigger", "min", "max", "fetchTeam", "value", "teamId", "params", "id", "response", "getTeam", "Object", "assign", "error", "console", "handleSubmit", "validate", "updateTeam", "success", "push", "handleStopRecruiting", "confirm", "confirmButtonText", "cancelButtonText", "type", "stopRecruiting", "status", "data", "handleCancel", "back"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\team\\TeamEditView.vue"], "sourcesContent": ["<template>\n  <div class=\"team-edit\">\n    <el-card>\n      <template #header>\n        <h3>编辑团队</h3>\n      </template>\n      \n      <div v-if=\"loading\" class=\"loading\">\n        <el-skeleton :rows=\"5\" animated />\n      </div>\n      \n      <el-form\n        v-else\n        ref=\"formRef\"\n        :model=\"form\"\n        :rules=\"rules\"\n        label-width=\"100px\"\n        size=\"large\"\n      >\n        <el-form-item label=\"团队名称\" prop=\"name\">\n          <el-input v-model=\"form.name\" placeholder=\"请输入团队名称\" />\n        </el-form-item>\n        \n        <el-form-item label=\"团队描述\" prop=\"description\">\n          <el-input\n            v-model=\"form.description\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入团队描述\"\n          />\n        </el-form-item>\n        \n        <el-form-item label=\"最大成员数\" prop=\"maxMembers\">\n          <el-input-number\n            v-model=\"form.maxMembers\"\n            :min=\"2\"\n            :max=\"20\"\n            placeholder=\"最大成员数\"\n          />\n        </el-form-item>\n        \n        <el-form-item>\n          <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"submitting\">\n            保存修改\n          </el-button>\n          <el-button\n            v-if=\"team && team.status === 'APPROVED'\"\n            type=\"warning\"\n            @click=\"handleStopRecruiting\"\n            :loading=\"stoppingRecruiting\"\n          >\n            停止招募\n          </el-button>\n          <el-button @click=\"handleCancel\">\n            取消\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRouter, useRoute } from 'vue-router'\nimport { teamAPI } from '@/api'\nimport { ElMessage, ElMessageBox } from 'element-plus'\n\nexport default {\n  name: 'TeamEditView',\n  setup() {\n    const router = useRouter()\n    const route = useRoute()\n    const formRef = ref()\n    const loading = ref(false)\n    const submitting = ref(false)\n    const stoppingRecruiting = ref(false)\n    const team = ref(null)\n    \n    const form = reactive({\n      name: '',\n      description: '',\n      maxMembers: 5\n    })\n    \n    const rules = {\n      name: [\n        { required: true, message: '请输入团队名称', trigger: 'blur' },\n        { min: 2, max: 50, message: '团队名称长度在 2 到 50 个字符', trigger: 'blur' }\n      ],\n      description: [\n        { required: true, message: '请输入团队描述', trigger: 'blur' },\n        { min: 10, max: 500, message: '团队描述长度在 10 到 500 个字符', trigger: 'blur' }\n      ],\n      maxMembers: [\n        { required: true, message: '请输入最大成员数', trigger: 'blur' }\n      ]\n    }\n    \n    const fetchTeam = async () => {\n      try {\n        loading.value = true\n        const teamId = route.params.id\n        const response = await teamAPI.getTeam(teamId)\n        team.value = response\n\n        Object.assign(form, {\n          name: team.value.name,\n          description: team.value.description,\n          maxMembers: team.value.maxMembers\n        })\n      } catch (error) {\n        console.error('Fetch team error:', error)\n        ElMessage.error('获取团队信息失败: ' + (error.message || '未知错误'))\n      } finally {\n        loading.value = false\n      }\n    }\n    \n    const handleSubmit = async () => {\n      if (!formRef.value) return\n      \n      try {\n        await formRef.value.validate()\n        submitting.value = true\n        \n        const teamId = route.params.id\n        await teamAPI.updateTeam(teamId, form)\n        ElMessage.success('团队更新成功！')\n        router.push('/dashboard/my-teams')\n      } catch (error) {\n        console.error('Update team error:', error)\n        ElMessage.error('更新团队失败')\n      } finally {\n        submitting.value = false\n      }\n    }\n    \n    const handleStopRecruiting = async () => {\n      try {\n        await ElMessageBox.confirm(\n          '停止招募后，其他用户将无法申请加入团队，但团队可以申请项目。确定要停止招募吗？',\n          '停止招募确认',\n          {\n            confirmButtonText: '确定停止',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        stoppingRecruiting.value = true\n        const teamId = route.params.id\n        await teamAPI.stopRecruiting(teamId)\n\n        ElMessage.success('停止招募成功！')\n        // 更新团队状态\n        team.value.status = 'RECRUITING_STOPPED'\n      } catch (error) {\n        if (error === 'cancel') {\n          return\n        }\n        console.error('Stop recruiting error:', error)\n        ElMessage.error('停止招募失败: ' + (error.response?.data?.message || error.message || '未知错误'))\n      } finally {\n        stoppingRecruiting.value = false\n      }\n    }\n\n    const handleCancel = () => {\n      router.back()\n    }\n    \n    onMounted(() => {\n      fetchTeam()\n    })\n    \n    return {\n      formRef,\n      form,\n      rules,\n      loading,\n      submitting,\n      stoppingRecruiting,\n      team,\n      handleSubmit,\n      handleStopRecruiting,\n      handleCancel\n    }\n  }\n}\n</script>\n\n<style scoped>\n.team-edit {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.loading {\n  padding: 20px;\n}\n</style>\n"], "mappings": ";AA+DA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AAC7C,SAASC,SAAS,EAAEC,QAAO,QAAS,YAAW;AAC/C,SAASC,OAAM,QAAS,OAAM;AAC9B,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AAErD,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAIP,SAAS,CAAC;IACzB,MAAMQ,KAAI,GAAIP,QAAQ,CAAC;IACvB,MAAMQ,OAAM,GAAIZ,GAAG,CAAC;IACpB,MAAMa,OAAM,GAAIb,GAAG,CAAC,KAAK;IACzB,MAAMc,UAAS,GAAId,GAAG,CAAC,KAAK;IAC5B,MAAMe,kBAAiB,GAAIf,GAAG,CAAC,KAAK;IACpC,MAAMgB,IAAG,GAAIhB,GAAG,CAAC,IAAI;IAErB,MAAMiB,IAAG,GAAIhB,QAAQ,CAAC;MACpBO,IAAI,EAAE,EAAE;MACRU,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE;IACd,CAAC;IAED,MAAMC,KAAI,GAAI;MACZZ,IAAI,EAAE,CACJ;QAAEa,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,EACvD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,oBAAoB;QAAEC,OAAO,EAAE;MAAO,EACnE;MACDL,WAAW,EAAE,CACX;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,EACvD;QAAEC,GAAG,EAAE,EAAE;QAAEC,GAAG,EAAE,GAAG;QAAEH,OAAO,EAAE,sBAAsB;QAAEC,OAAO,EAAE;MAAO,EACvE;MACDJ,UAAU,EAAE,CACV;QAAEE,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,UAAU;QAAEC,OAAO,EAAE;MAAO;IAE3D;IAEA,MAAMG,SAAQ,GAAI,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFb,OAAO,CAACc,KAAI,GAAI,IAAG;QACnB,MAAMC,MAAK,GAAIjB,KAAK,CAACkB,MAAM,CAACC,EAAC;QAC7B,MAAMC,QAAO,GAAI,MAAM1B,OAAO,CAAC2B,OAAO,CAACJ,MAAM;QAC7CZ,IAAI,CAACW,KAAI,GAAII,QAAO;QAEpBE,MAAM,CAACC,MAAM,CAACjB,IAAI,EAAE;UAClBT,IAAI,EAAEQ,IAAI,CAACW,KAAK,CAACnB,IAAI;UACrBU,WAAW,EAAEF,IAAI,CAACW,KAAK,CAACT,WAAW;UACnCC,UAAU,EAAEH,IAAI,CAACW,KAAK,CAACR;QACzB,CAAC;MACH,EAAE,OAAOgB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK;QACxC7B,SAAS,CAAC6B,KAAK,CAAC,YAAW,IAAKA,KAAK,CAACb,OAAM,IAAK,MAAM,CAAC;MAC1D,UAAU;QACRT,OAAO,CAACc,KAAI,GAAI,KAAI;MACtB;IACF;IAEA,MAAMU,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/B,IAAI,CAACzB,OAAO,CAACe,KAAK,EAAE;MAEpB,IAAI;QACF,MAAMf,OAAO,CAACe,KAAK,CAACW,QAAQ,CAAC;QAC7BxB,UAAU,CAACa,KAAI,GAAI,IAAG;QAEtB,MAAMC,MAAK,GAAIjB,KAAK,CAACkB,MAAM,CAACC,EAAC;QAC7B,MAAMzB,OAAO,CAACkC,UAAU,CAACX,MAAM,EAAEX,IAAI;QACrCX,SAAS,CAACkC,OAAO,CAAC,SAAS;QAC3B9B,MAAM,CAAC+B,IAAI,CAAC,qBAAqB;MACnC,EAAE,OAAON,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK;QACzC7B,SAAS,CAAC6B,KAAK,CAAC,QAAQ;MAC1B,UAAU;QACRrB,UAAU,CAACa,KAAI,GAAI,KAAI;MACzB;IACF;IAEA,MAAMe,oBAAmB,GAAI,MAAAA,CAAA,KAAY;MACvC,IAAI;QACF,MAAMnC,YAAY,CAACoC,OAAO,CACxB,yCAAyC,EACzC,QAAQ,EACR;UACEC,iBAAiB,EAAE,MAAM;UACzBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE;QACR,CACF;QAEA/B,kBAAkB,CAACY,KAAI,GAAI,IAAG;QAC9B,MAAMC,MAAK,GAAIjB,KAAK,CAACkB,MAAM,CAACC,EAAC;QAC7B,MAAMzB,OAAO,CAAC0C,cAAc,CAACnB,MAAM;QAEnCtB,SAAS,CAACkC,OAAO,CAAC,SAAS;QAC3B;QACAxB,IAAI,CAACW,KAAK,CAACqB,MAAK,GAAI,oBAAmB;MACzC,EAAE,OAAOb,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtB;QACF;QACAC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK;QAC7C7B,SAAS,CAAC6B,KAAK,CAAC,UAAS,IAAKA,KAAK,CAACJ,QAAQ,EAAEkB,IAAI,EAAE3B,OAAM,IAAKa,KAAK,CAACb,OAAM,IAAK,MAAM,CAAC;MACzF,UAAU;QACRP,kBAAkB,CAACY,KAAI,GAAI,KAAI;MACjC;IACF;IAEA,MAAMuB,YAAW,GAAIA,CAAA,KAAM;MACzBxC,MAAM,CAACyC,IAAI,CAAC;IACd;IAEAjD,SAAS,CAAC,MAAM;MACdwB,SAAS,CAAC;IACZ,CAAC;IAED,OAAO;MACLd,OAAO;MACPK,IAAI;MACJG,KAAK;MACLP,OAAO;MACPC,UAAU;MACVC,kBAAkB;MAClBC,IAAI;MACJqB,YAAY;MACZK,oBAAoB;MACpBQ;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}