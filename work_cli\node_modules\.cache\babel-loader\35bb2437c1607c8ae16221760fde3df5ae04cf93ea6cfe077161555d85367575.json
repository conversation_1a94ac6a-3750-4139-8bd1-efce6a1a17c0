{"ast": null, "code": "export default {\n  name: 'FilterBar'\n};", "map": {"version": 3, "names": ["name"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\components\\FilterBar.vue"], "sourcesContent": ["<template>\n  <div class=\"filter-bar\">\n    <div class=\"left\">\n      <slot />\n    </div>\n    <div class=\"right\">\n      <slot name=\"actions\" />\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FilterBar'\n}\n</script>\n\n<style scoped>\n.filter-bar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: 12px;\n  padding: 8px 0 12px 0;\n}\n.left, .right {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n</style>\n\n"], "mappings": "AAYA,eAAe;EACbA,IAAI,EAAE;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}