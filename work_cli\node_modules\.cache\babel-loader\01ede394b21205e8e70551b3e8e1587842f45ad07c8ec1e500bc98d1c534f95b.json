{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderSlot as _renderSlot } from \"vue\";\nconst _hoisted_1 = {\n  class: \"page-header\"\n};\nconst _hoisted_2 = {\n  class: \"left\"\n};\nconst _hoisted_3 = {\n  class: \"title\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"subtitle\"\n};\nconst _hoisted_5 = {\n  class: \"right\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, _toDisplayString($props.title), 1 /* TEXT */), $props.subtitle ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, _toDisplayString($props.subtitle), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_5, [_renderSlot(_ctx.$slots, \"actions\", {}, undefined, true)])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "$props", "title", "subtitle", "_hoisted_4", "_hoisted_5", "_renderSlot", "_ctx", "$slots", "undefined"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\components\\PageHeader.vue"], "sourcesContent": ["<template>\n  <div class=\"page-header\">\n    <div class=\"left\">\n      <div class=\"title\">{{ title }}</div>\n      <div v-if=\"subtitle\" class=\"subtitle\">{{ subtitle }}</div>\n    </div>\n    <div class=\"right\">\n      <slot name=\"actions\" />\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'PageHeader',\n  props: {\n    title: { type: String, required: true },\n    subtitle: { type: String, default: '' }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 12px 4px 16px 4px;\n}\n.title {\n  font-size: 20px;\n  font-weight: 600;\n}\n.subtitle {\n  margin-top: 4px;\n  color: #909399;\n  font-size: 12px;\n}\n.right {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n</style>\n\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAO;;;EACGA,KAAK,EAAC;;;EAExBA,KAAK,EAAC;AAAO;;uBALpBC,mBAAA,CAQM,OARNC,UAQM,GAPJC,mBAAA,CAGM,OAHNC,UAGM,GAFJD,mBAAA,CAAoC,OAApCE,UAAoC,EAAAC,gBAAA,CAAdC,MAAA,CAAAC,KAAK,kBAChBD,MAAA,CAAAE,QAAQ,I,cAAnBR,mBAAA,CAA0D,OAA1DS,UAA0D,EAAAJ,gBAAA,CAAjBC,MAAA,CAAAE,QAAQ,oB,qCAEnDN,mBAAA,CAEM,OAFNQ,UAEM,GADJC,WAAA,CAAuBC,IAAA,CAAAC,MAAA,iBAAAC,SAAA,Q", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}