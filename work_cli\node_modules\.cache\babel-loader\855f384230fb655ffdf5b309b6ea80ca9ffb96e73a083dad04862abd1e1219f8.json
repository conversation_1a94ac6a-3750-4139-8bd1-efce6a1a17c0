{"ast": null, "code": "export default {\n  name: 'Page<PERSON>eader',\n  props: {\n    title: {\n      type: String,\n      required: true\n    },\n    subtitle: {\n      type: String,\n      default: ''\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "title", "type", "String", "required", "subtitle", "default"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\components\\PageHeader.vue"], "sourcesContent": ["<template>\n  <div class=\"page-header\">\n    <div class=\"left\">\n      <div class=\"title\">{{ title }}</div>\n      <div v-if=\"subtitle\" class=\"subtitle\">{{ subtitle }}</div>\n    </div>\n    <div class=\"right\">\n      <slot name=\"actions\" />\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'PageHeader',\n  props: {\n    title: { type: String, required: true },\n    subtitle: { type: String, default: '' }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 12px 4px 16px 4px;\n}\n.title {\n  font-size: 20px;\n  font-weight: 600;\n}\n.subtitle {\n  margin-top: 4px;\n  color: #909399;\n  font-size: 12px;\n}\n.right {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n</style>\n\n"], "mappings": "AAaA,eAAe;EACbA,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE;IACLC,KAAK,EAAE;MAAEC,IAAI,EAAEC,MAAM;MAAEC,QAAQ,EAAE;IAAK,CAAC;IACvCC,QAAQ,EAAE;MAAEH,IAAI,EAAEC,MAAM;MAAEG,OAAO,EAAE;IAAG;EACxC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}