{"ast": null, "code": "import { renderSlot as _renderSlot, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"filter-bar\"\n};\nconst _hoisted_2 = {\n  class: \"left\"\n};\nconst _hoisted_3 = {\n  class: \"right\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_renderSlot(_ctx.$slots, \"default\", {}, undefined, true)]), _createElementVNode(\"div\", _hoisted_3, [_renderSlot(_ctx.$slots, \"actions\", {}, undefined, true)])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_renderSlot", "_ctx", "$slots", "undefined", "_hoisted_3"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\components\\FilterBar.vue"], "sourcesContent": ["<template>\n  <div class=\"filter-bar\">\n    <div class=\"left\">\n      <slot />\n    </div>\n    <div class=\"right\">\n      <slot name=\"actions\" />\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FilterBar'\n}\n</script>\n\n<style scoped>\n.filter-bar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: 12px;\n  padding: 8px 0 12px 0;\n}\n.left, .right {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n</style>\n\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAM;;EAGZA,KAAK,EAAC;AAAO;;uBAJpBC,mBAAA,CAOM,OAPNC,UAOM,GANJC,mBAAA,CAEM,OAFNC,UAEM,GADJC,WAAA,CAAQC,IAAA,CAAAC,MAAA,iBAAAC,SAAA,Q,GAEVL,mBAAA,CAEM,OAFNM,UAEM,GADJJ,WAAA,CAAuBC,IAAA,CAAAC,MAAA,iBAAAC,SAAA,Q", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}