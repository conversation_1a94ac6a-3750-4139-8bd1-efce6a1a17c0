<template>
  <div class="team-edit">
    <el-card>
      <template #header>
        <h3>编辑团队</h3>
      </template>
      
      <div v-if="loading" class="loading">
        <el-skeleton :rows="5" animated />
      </div>
      
      <el-form
        v-else
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        size="large"
      >
        <el-form-item label="团队名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入团队名称" />
        </el-form-item>
        
        <el-form-item label="团队描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入团队描述"
          />
        </el-form-item>
        
        <el-form-item label="最大成员数" prop="maxMembers">
          <el-input-number
            v-model="form.maxMembers"
            :min="2"
            :max="20"
            placeholder="最大成员数"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            保存修改
          </el-button>
          <el-button
            v-if="team && team.status === 'APPROVED'"
            type="warning"
            @click="handleStopRecruiting"
            :loading="stoppingRecruiting"
          >
            停止招募
          </el-button>
          <el-button @click="handleCancel">
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { teamAPI } from '@/api'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'TeamEditView',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref()
    const loading = ref(false)
    const submitting = ref(false)
    const stoppingRecruiting = ref(false)
    const team = ref(null)
    
    const form = reactive({
      name: '',
      description: '',
      maxMembers: 5
    })
    
    const rules = {
      name: [
        { required: true, message: '请输入团队名称', trigger: 'blur' },
        { min: 2, max: 50, message: '团队名称长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      description: [
        { required: true, message: '请输入团队描述', trigger: 'blur' },
        { min: 10, max: 500, message: '团队描述长度在 10 到 500 个字符', trigger: 'blur' }
      ],
      maxMembers: [
        { required: true, message: '请输入最大成员数', trigger: 'blur' }
      ]
    }
    
    const fetchTeam = async () => {
      try {
        loading.value = true
        const teamId = route.params.id
        const response = await teamAPI.getTeam(teamId)
        team.value = response

        Object.assign(form, {
          name: team.value.name,
          description: team.value.description,
          maxMembers: team.value.maxMembers
        })
      } catch (error) {
        console.error('Fetch team error:', error)
        ElMessage.error('获取团队信息失败: ' + (error.message || '未知错误'))
      } finally {
        loading.value = false
      }
    }
    
    const handleSubmit = async () => {
      if (!formRef.value) return
      
      try {
        await formRef.value.validate()
        submitting.value = true
        
        const teamId = route.params.id
        await teamAPI.updateTeam(teamId, form)
        ElMessage.success('团队更新成功！')
        router.push('/dashboard/my-teams')
      } catch (error) {
        console.error('Update team error:', error)
        ElMessage.error('更新团队失败')
      } finally {
        submitting.value = false
      }
    }
    
    const handleStopRecruiting = async () => {
      try {
        await ElMessageBox.confirm(
          '停止招募后，其他用户将无法申请加入团队，但团队可以申请项目。确定要停止招募吗？',
          '停止招募确认',
          {
            confirmButtonText: '确定停止',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        stoppingRecruiting.value = true
        const teamId = route.params.id
        await teamAPI.stopRecruiting(teamId)

        ElMessage.success('停止招募成功！')
        // 更新团队状态
        team.value.status = 'RECRUITING_STOPPED'
      } catch (error) {
        if (error === 'cancel') {
          return
        }
        console.error('Stop recruiting error:', error)
        ElMessage.error('停止招募失败: ' + (error.response?.data?.message || error.message || '未知错误'))
      } finally {
        stoppingRecruiting.value = false
      }
    }

    const handleCancel = () => {
      router.back()
    }
    
    onMounted(() => {
      fetchTeam()
    })
    
    return {
      formRef,
      form,
      rules,
      loading,
      submitting,
      stoppingRecruiting,
      team,
      handleSubmit,
      handleStopRecruiting,
      handleCancel
    }
  }
}
</script>

<style scoped>
.team-edit {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.loading {
  padding: 20px;
}
</style>
